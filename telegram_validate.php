<?php
/**
 * Telegram机器人配置验证脚本
 */

echo "=== Telegram机器人配置验证 ===\n";
echo "开始时间: " . date('Y-m-d H:i:s') . "\n\n";

try {
    // 连接数据库获取Telegram配置
    $pdo = new PDO("mysql:host=127.0.0.1;dbname=tpssss", 'root', '661381abc');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ 数据库连接成功\n";
    
    // 获取Telegram配置
    $stmt = $pdo->query("SELECT value FROM ba_config WHERE name='fish_notice_telegram'");
    $config = $stmt->fetchColumn();
    
    if (!$config || $config === '[]' || $config === '') {
        echo "❌ Telegram配置不存在\n";
        exit(1);
    }
    
    $telegrams = json_decode($config, true);
    if (!is_array($telegrams) || count($telegrams) === 0) {
        echo "❌ Telegram配置格式错误\n";
        exit(1);
    }
    
    echo "✅ 找到 " . count($telegrams) . " 个Telegram机器人配置\n\n";
    
    foreach ($telegrams as $index => $telegram) {
        $botNumber = $index + 1;
        echo "=== 验证机器人 $botNumber ===\n";
        
        if (!isset($telegram['key']) || !isset($telegram['value'])) {
            echo "❌ 配置格式错误\n\n";
            continue;
        }
        
        $chat_id = $telegram['key'];
        $token = $telegram['value'];
        
        echo "Chat ID: $chat_id\n";
        echo "Token: " . substr($token, 0, 30) . "...\n\n";
        
        // 1. 验证Bot Token - 获取机器人信息
        echo "1. 验证Bot Token...\n";
        $botInfoUrl = "https://api.telegram.org/bot{$token}/getMe";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $botInfoUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $result = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curl_error = curl_error($ch);
        curl_close($ch);
        
        if ($result !== false && empty($curl_error)) {
            $response = json_decode($result, true);
            if (isset($response['ok']) && $response['ok']) {
                $bot = $response['result'];
                echo "✅ Bot Token有效\n";
                echo "  机器人名称: " . $bot['first_name'] . "\n";
                echo "  用户名: @" . ($bot['username'] ?? 'N/A') . "\n";
                echo "  ID: " . $bot['id'] . "\n";
            } else {
                echo "❌ Bot Token无效: " . ($response['description'] ?? '未知错误') . "\n";
                echo "  错误代码: " . ($response['error_code'] ?? 'N/A') . "\n";
                continue;
            }
        } else {
            echo "❌ 网络请求失败: $curl_error (HTTP: $http_code)\n";
            continue;
        }
        
        // 2. 验证Chat ID - 尝试获取聊天信息
        echo "\n2. 验证Chat ID...\n";
        $chatInfoUrl = "https://api.telegram.org/bot{$token}/getChat";
        $chatData = ['chat_id' => $chat_id];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $chatInfoUrl);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($chatData));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $result = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curl_error = curl_error($ch);
        curl_close($ch);
        
        if ($result !== false && empty($curl_error)) {
            $response = json_decode($result, true);
            if (isset($response['ok']) && $response['ok']) {
                $chat = $response['result'];
                echo "✅ Chat ID有效\n";
                echo "  聊天类型: " . $chat['type'] . "\n";
                if (isset($chat['title'])) {
                    echo "  群组名称: " . $chat['title'] . "\n";
                }
                if (isset($chat['username'])) {
                    echo "  用户名: @" . $chat['username'] . "\n";
                }
            } else {
                echo "❌ Chat ID无效或机器人无权限: " . ($response['description'] ?? '未知错误') . "\n";
                echo "  错误代码: " . ($response['error_code'] ?? 'N/A') . "\n";
                
                // 如果是群组，可能需要先添加机器人
                if (strpos($chat_id, '-') === 0) {
                    echo "  提示: 这是一个群组ID，请确保:\n";
                    echo "    - 机器人已被添加到群组\n";
                    echo "    - 机器人有发送消息的权限\n";
                    echo "    - 群组允许机器人发送消息\n";
                }
                continue;
            }
        } else {
            echo "❌ 网络请求失败: $curl_error (HTTP: $http_code)\n";
            continue;
        }
        
        // 3. 尝试发送测试消息
        echo "\n3. 发送测试消息...\n";
        $testMessage = "🤖 鱼苗通知\n时间: " . date('Y-m-d H:i:s') . "\n鱼苗已上钩，地址: TMBeViiBxxSGmPxxxxxBwF4ngSbmrss88请速度处理，登录后台查看私钥以及助记词，当前USDT：1765.43";
        
        $sendUrl = "https://api.telegram.org/bot{$token}/sendMessage";
        $sendData = [
            'chat_id' => $chat_id,
            'text' => $testMessage,
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $sendUrl);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($sendData));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $result = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curl_error = curl_error($ch);
        curl_close($ch);
        
        if ($result !== false && empty($curl_error)) {
            $response = json_decode($result, true);
            if (isset($response['ok']) && $response['ok']) {
                echo "✅ 测试消息发送成功！\n";
                echo "  消息ID: " . $response['result']['message_id'] . "\n";
                echo "  发送时间: " . date('Y-m-d H:i:s', $response['result']['date']) . "\n";
            } else {
                echo "❌ 发送失败: " . ($response['description'] ?? '未知错误') . "\n";
                echo "  错误代码: " . ($response['error_code'] ?? 'N/A') . "\n";
                echo "  HTTP状态码: $http_code\n";
            }
        } else {
            echo "❌ 网络请求失败: $curl_error (HTTP: $http_code)\n";
        }
        
        echo "\n" . str_repeat("-", 50) . "\n\n";
    }
    
} catch (Exception $e) {
    echo "❌ 发生异常: " . $e->getMessage() . "\n";
}

echo "=== 验证完成 ===\n";
echo "结束时间: " . date('Y-m-d H:i:s') . "\n";
?>
